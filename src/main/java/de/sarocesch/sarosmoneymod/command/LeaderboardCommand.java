package de.sarocesch.sarosmoneymod.command;

import com.mojang.authlib.GameProfile;
import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.IntegerArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import de.sarocesch.sarosmoneymod.data.BalanceManager;
import net.minecraft.ChatFormatting;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.core.BlockPos;
import net.minecraft.core.HolderLookup;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.nbt.Tag;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.util.datafix.DataFixTypes;
import net.minecraft.world.entity.decoration.ArmorStand;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.saveddata.SavedData;
import net.neoforged.bus.api.SubscribeEvent;
import net.neoforged.fml.common.Mod;
import net.neoforged.neoforge.event.RegisterCommandsEvent;
import net.neoforged.neoforge.event.entity.EntityJoinLevelEvent;
import net.neoforged.neoforge.event.tick.ServerTickEvent;

import java.util.*;
import java.util.stream.Collectors;

@Mod.EventBusSubscriber
public class LeaderboardCommand {

    private static LeaderboardData leaderboardData;

    @SubscribeEvent
    public static void registerCommand(RegisterCommandsEvent event) {
        CommandDispatcher<CommandSourceStack> dispatcher = event.getDispatcher();
        dispatcher.register(Commands.literal("leaderboard")
                .then(Commands.literal("create")
                        .then(Commands.argument("number", IntegerArgumentType.integer(1))
                                .executes(context -> createLeaderboard(context, IntegerArgumentType.getInteger(context, "number")))))
                .then(Commands.literal("delete")
                        .executes(LeaderboardCommand::deleteLeaderboards))
        );
    }

    private static int createLeaderboard(CommandContext<CommandSourceStack> context, int number) {
        CommandSourceStack source = context.getSource();
        try {
            ServerPlayer player = source.getPlayerOrException();
            ServerLevel world = player.serverLevel();

            if (leaderboardData == null) {
                leaderboardData = LeaderboardData.load(world);
            }

            BlockPos position = player.blockPosition();

            Leaderboard newLeaderboard = new Leaderboard(position, number);
            leaderboardData.addLeaderboard(newLeaderboard);

            updateLeaderboard(world, newLeaderboard);

            leaderboardData.setDirty(); // Mark data as changed
            source.sendSuccess(
                    () -> Component.literal("Leaderboard created successfully.").withStyle(ChatFormatting.GREEN),
                    false
            );
        } catch (CommandSyntaxException e) {
            source.sendFailure(
                    Component.literal("Failed to create leaderboard.").withStyle(ChatFormatting.RED)
            );
        }
        return 1;
    }

    private static int deleteLeaderboards(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();
        try {
            ServerPlayer player = source.getPlayerOrException();
            ServerLevel world = player.serverLevel();

            if (leaderboardData == null) {
                leaderboardData = LeaderboardData.load(world);
            }

            BlockPos playerPos = player.blockPosition();
            int radius = 5;

            List<Leaderboard> toRemove = leaderboardData.getLeaderboards().stream()
                    .filter(lb -> lb.getPosition().closerThan(playerPos, radius))
                    .collect(Collectors.toList());

            for (Leaderboard leaderboard : toRemove) {
                leaderboard.getArmorStands().forEach(armorStand -> {
                    if (armorStand != null && !armorStand.isRemoved()) {
                        armorStand.kill();
                    }
                });
                leaderboardData.removeLeaderboard(leaderboard);
            }

            leaderboardData.setDirty(); // Mark data as changed
            source.sendSuccess(
                    () -> Component.literal("Leaderboards deleted successfully.").withStyle(ChatFormatting.GREEN),
                    false
            );
        } catch (CommandSyntaxException e) {
            source.sendFailure(
                    Component.literal("Failed to delete leaderboards.").withStyle(ChatFormatting.RED)
            );
        }
        return 1;
    }

    @SubscribeEvent
    public static void onServerTick(ServerTickEvent.Post event) {
        if (event.getServer() != null) {
            ServerLevel world = event.getServer().getLevel(Level.OVERWORLD);
            if (world != null) {
                if (leaderboardData == null) {
                    leaderboardData = LeaderboardData.load(world);
                }
                for (Leaderboard leaderboard : leaderboardData.getLeaderboards()) {
                    updateLeaderboard(world, leaderboard);
                }
            }
        }
    }

    @SubscribeEvent
    public static void onEntityJoin(EntityJoinLevelEvent event) {
        if (event.getEntity() instanceof ArmorStand && event.getLevel() instanceof ServerLevel) {
            ServerLevel world = (ServerLevel) event.getLevel();

            if (leaderboardData == null) {
                leaderboardData = LeaderboardData.load(world);
            }

            ArmorStand armorStand = (ArmorStand) event.getEntity();
            BlockPos armorStandPos = armorStand.blockPosition();

            // Relink ArmorStand to its respective leaderboard
            leaderboardData.getLeaderboards().forEach(leaderboard -> {
                if (leaderboard.getPosition().equals(armorStandPos)) {
                    leaderboard.addArmorStand(armorStand);
                }
            });
        }
    }

    private static void updateLeaderboard(Level world, Leaderboard leaderboard) {
        Map<String, Double> balances = BalanceManager.loadBalances(world.getServer());
        List<Map.Entry<String, Double>> sortedBalances = balances.entrySet().stream()
                .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
                .limit(leaderboard.getSize())
                .collect(Collectors.toList());

        List<String> leaderboardLines = new ArrayList<>();
        int i = 1;
        for (Map.Entry<String, Double> entry : sortedBalances) {
            try {
                UUID playerUUID = UUID.fromString(entry.getKey());
                Optional<GameProfile> profile = world.getServer().getProfileCache().get(playerUUID);
                String playerName = profile.map(GameProfile::getName).orElse("Unknown Player");
                leaderboardLines.add(i + ". " + playerName + ": " + String.format("%.2f", entry.getValue()));
                i++;
            } catch (IllegalArgumentException e) {
                System.err.println("Invalid UUID string: " + entry.getKey());
            }
        }

        leaderboard.getArmorStands().forEach(armorStand -> {
            if (armorStand != null && !armorStand.isRemoved()) {
                armorStand.kill();
            }
        });
        leaderboard.clearArmorStands();

        BlockPos position = leaderboard.getPosition();
        for (int j = 0; j < leaderboardLines.size(); j++) {
            String line = leaderboardLines.get(j);
            ArmorStand armorStand = new ArmorStand(world, position.getX(), position.getY() - (j * 0.25), position.getZ());
            armorStand.setInvisible(true);
            armorStand.setCustomName(Component.literal(line));
            armorStand.setCustomNameVisible(true);
            armorStand.setNoGravity(true);
            world.addFreshEntity(armorStand);
            leaderboard.addArmorStand(armorStand);
        }
    }

    private static class Leaderboard {
        private final BlockPos position;
        private final int size;
        private final List<ArmorStand> armorStands;

        public Leaderboard(BlockPos position, int size) {
            this.position = position;
            this.size = size;
            this.armorStands = new ArrayList<>();
        }

        public BlockPos getPosition() {
            return position;
        }

        public int getSize() {
            return size;
        }

        public List<ArmorStand> getArmorStands() {
            return armorStands;
        }

        public void addArmorStand(ArmorStand armorStand) {
            armorStands.add(armorStand);
        }

        public void clearArmorStands() {
            armorStands.clear();
        }

        public CompoundTag toTag() {
            CompoundTag tag = new CompoundTag();
            tag.putInt("x", position.getX());
            tag.putInt("y", position.getY());
            tag.putInt("z", position.getZ());
            tag.putInt("size", size);
            return tag;
        }

        public static Leaderboard fromTag(CompoundTag tag) {
            BlockPos pos = new BlockPos(tag.getInt("x"), tag.getInt("y"), tag.getInt("z"));
            int size = tag.getInt("size");
            return new Leaderboard(pos, size);
        }
    }

    private static class LeaderboardData extends SavedData {
        private static final String DATA_NAME = "leaderboard_data";
        private final List<Leaderboard> leaderboards = new ArrayList<>();

        public LeaderboardData() {
            // Leerer Konstruktor
        }

        public List<Leaderboard> getLeaderboards() {
            return leaderboards;
        }

        public void addLeaderboard(Leaderboard leaderboard) {
            leaderboards.add(leaderboard);
            setDirty();
        }

        public void removeLeaderboard(Leaderboard leaderboard) {
            leaderboards.remove(leaderboard);
            setDirty();
        }

        @Override
        public CompoundTag save(CompoundTag tag, HolderLookup.Provider provider) {
            ListTag tags = new ListTag();
            leaderboards.stream()
                    .map(Leaderboard::toTag)
                    .forEach(tags::add);
            tag.put("leaderboards", tags);
            return tag;
        }

        public static LeaderboardData load(CompoundTag tag, HolderLookup.Provider provider) {
            LeaderboardData data = new LeaderboardData();
            if (tag.contains("leaderboards", Tag.TAG_LIST)) {
                ListTag tags = tag.getList("leaderboards", Tag.TAG_COMPOUND);
                for (int i = 0; i < tags.size(); i++) {
                    data.addLeaderboard(Leaderboard.fromTag(tags.getCompound(i)));
                }
            }
            return data;
        }

        public static LeaderboardData load(ServerLevel world) {
            return world.getDataStorage().computeIfAbsent(
                    new SavedData.Factory<>(
                            LeaderboardData::new,
                            (tag, provider) -> load(tag, provider),
                            DataFixTypes.LEVEL
                    ),
                    DATA_NAME
            );
        }
    }
}