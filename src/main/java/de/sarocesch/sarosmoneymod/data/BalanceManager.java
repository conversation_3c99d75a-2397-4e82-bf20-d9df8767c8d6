package de.sarocesch.sarosmoneymod.data;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.neoforged.fml.common.EventBusSubscriber;
import java.util.Map;

@EventBusSubscriber(bus = EventBusSubscriber.Bus.MOD)
public class BalanceManager {

    /**
     * Fügt dem Spieler über das SavedData-System einen bestimmten Betrag hinzu.
     */
    public static void addMoneyToPlayer(MinecraftServer server, String playerUUID, double amount) {
        double current = BalanceManager.getBalance(playerUUID);
        BalanceManager.setBalance(playerUUID, current + amount);
    }

    /**
     * Gibt alle Spieler-G<PERSON>aben als Kopie der internen Map zurück.
     */
    public static Map<String, Double> loadBalances(MinecraftServer server) {
        return BalanceData.get(server).getAllBalances();
    }

    // Schnittstelle zur Nutzung in Commands:

    /**
     * Liest den Kontostand des Spielers.
     *
     * @param player der ServerPlayer
     * @return aktueller Kontostand
     */
    public static double getPlayerBalance(ServerPlayer player) {
        return BalanceManager.getBalance(player.getUUID().toString());
    }

    /**
     * Aktualisiert den Kontostand des Spielers.
     *
     * @param player der ServerPlayer
     * @param newBalance der neue Kontostand
     */
    public static void updatePlayerBalance(ServerPlayer player, double newBalance) {
        BalanceManager.setBalance(player.getUUID().toString(), newBalance);
    }


    // Liefert den aktuellen Kontostand des Spielers
    public static double getBalance(String playerUUID) {
        MinecraftServer server = ServerLifecycleHooks.getCurrentServer();
        if (server == null) {
            return 0.0;
        }
        BalanceData data = BalanceData.get(server);
        return data.getBalance(playerUUID);
    }

    // Aktualisiert den Kontostand des Spielers und markiert die Daten als "dirty"
    public static void setBalance(String playerUUID, double balance) {
        MinecraftServer server = ServerLifecycleHooks.getCurrentServer();
        if (server == null) {
            return;
        }
        BalanceData data = BalanceData.get(server);
        data.setBalance(playerUUID, balance);
    }

}
