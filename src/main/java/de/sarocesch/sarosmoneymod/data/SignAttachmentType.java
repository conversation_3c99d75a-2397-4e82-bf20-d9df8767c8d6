package de.sarocesch.sarosmoneymod.data;

import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.level.block.entity.SignBlockEntity;
import net.minecraft.world.item.component.CustomData;
import net.neoforged.bus.api.SubscribeEvent;
import net.neoforged.fml.common.EventBusSubscriber;
import net.neoforged.neoforge.registries.RegisterEvent;
import net.minecraft.core.RegistryAccess;
import net.minecraft.core.HolderLookup;

import java.util.UUID;

@EventBusSubscriber(modid = SarosMoneyModMod.MODID)
public class SignAttachmentType {

    // Konstante für den Schlüssel, unter dem die SignData gespeichert wird
    private static final String SIGN_DATA_KEY = "SignData";

    // Statische Instanz für einfachen Zugriff
    public static final SignData SIGN_DATA = new SignData();

    // Methode zum Abrufen der SignData aus einem SignBlockEntity
    public static SignData getData(SignBlockEntity sign) {
        // Versuche, die Daten aus dem NBT-Tag zu holen
        // In Forge 1.21 benötigt saveWithoutMetadata einen Provider-Parameter
        RegistryAccess registryAccess = sign.getLevel().registryAccess();
        CompoundTag blockEntityTag = sign.saveWithoutMetadata(registryAccess);
        if (blockEntityTag.contains(SIGN_DATA_KEY)) {
            SignData data = new SignData();
            CompoundTag signDataTag = blockEntityTag.getCompound(SIGN_DATA_KEY);
            data.deserializeFromNBT(signDataTag);
            return data;
        }

        // Wenn keine Daten gefunden wurden, gib eine leere SignData zurück
        return new SignData();
    }

    // Methode zum Speichern der SignData in einem SignBlockEntity
    public static void setData(SignBlockEntity sign, SignData data) {
        // Hole den aktuellen NBT-Tag
        // In Forge 1.21 benötigt saveWithoutMetadata einen Provider-Parameter
        RegistryAccess registryAccess = sign.getLevel().registryAccess();
        CompoundTag blockEntityTag = sign.saveWithoutMetadata(registryAccess);

        // Speichere die SignData
        CompoundTag signDataTag = new CompoundTag();
        data.serializeToNBT(signDataTag);
        blockEntityTag.put(SIGN_DATA_KEY, signDataTag);

        // Lade die Daten zurück in das SignBlockEntity
        // In Forge 1.21 benötigt load einen Provider-Parameter
        try {
            // Versuche, die Methode mit Reflection aufzurufen
            java.lang.reflect.Method method = SignBlockEntity.class.getMethod("load", CompoundTag.class, RegistryAccess.class);
            method.invoke(sign, blockEntityTag, registryAccess);
        } catch (Exception e) {
            // Fallback: Manuelles Laden der Daten
            SarosMoneyModMod.LOGGER.error("Fehler beim Laden der SignData: " + e.getMessage());
        }
        sign.setChanged();
    }

    // Die SignData-Klasse, die die Daten für ein Schild enthält
    public static class SignData {
        private UUID ownerUUID;
        private String shopType = "";
        private String itemId = "";
        private int price = 0;
        private int amount = 0;

        public SignData() {
            // Leerer Konstruktor
        }

        // Getter und Setter
        public UUID getOwnerUUID() {
            return ownerUUID;
        }

        public void setOwnerUUID(UUID ownerUUID) {
            this.ownerUUID = ownerUUID;
        }

        public String getShopType() {
            return shopType;
        }

        public void setShopType(String shopType) {
            this.shopType = shopType;
        }

        public String getItemId() {
            return itemId;
        }

        public void setItemId(String itemId) {
            this.itemId = itemId;
        }

        public int getPrice() {
            return price;
        }

        public void setPrice(int price) {
            this.price = price;
        }

        public int getAmount() {
            return amount;
        }

        public void setAmount(int amount) {
            this.amount = amount;
        }

        // Methode zum Serialisieren in einen NBT-Tag
        public void serializeToNBT(CompoundTag tag) {
            if (ownerUUID != null) {
                tag.putUUID("OwnerUUID", ownerUUID);
            }
            tag.putString("ShopType", shopType);
            tag.putString("ItemId", itemId);
            tag.putInt("Price", price);
            tag.putInt("Amount", amount);
        }

        // Methode zum Deserialisieren aus einem NBT-Tag
        public void deserializeFromNBT(CompoundTag tag) {
            if (tag.hasUUID("OwnerUUID")) {
                ownerUUID = tag.getUUID("OwnerUUID");
            }
            shopType = tag.getString("ShopType");
            itemId = tag.getString("ItemId");
            price = tag.getInt("Price");
            amount = tag.getInt("Amount");
        }
    }
}
