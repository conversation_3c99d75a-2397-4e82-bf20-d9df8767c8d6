package de.sarocesch.sarosmoneymod.init;

import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import de.sarocesch.sarosmoneymod.block.ATMBlock;
import de.sarocesch.sarosmoneymod.block.ATM2Block;
import de.sarocesch.sarosmoneymod.block.ATM3Block;
import de.sarocesch.sarosmoneymod.block.BankerBlock;
import net.minecraft.core.registries.BuiltInRegistries;
import net.minecraft.world.level.block.Block;
import net.neoforged.neoforge.registries.DeferredHolder;
import net.neoforged.neoforge.registries.DeferredRegister;

public class SarosMoneyModModBlocks {
	public static final DeferredRegister<Block> REGISTRY =
			DeferredRegister.create(BuiltInRegistries.BLOCK, SarosMoneyModMod.MODID);

	public static final DeferredHolder<Block, Block> ATM =
			REGISTRY.register("atm", ATMBlock::new);

	public static final DeferredHolder<Block, Block> ATM_2 =
			REGISTRY.register("atm_2", ATM2Block::new);

	public static final DeferredHolder<Block, Block> ATM_3 =
			REGISTRY.register("atm_3", ATM3Block::new);

	public static final DeferredHolder<Block, Block> BANKER =
			REGISTRY.register("banker", BankerBlock::new);
}