package de.sarocesch.sarosmoneymod.listener;

import de.sarocesch.sarosmoneymod.block.ATMBlock;
import de.sarocesch.sarosmoneymod.block.ATM2Block;
import de.sarocesch.sarosmoneymod.block.ATM3Block;
import de.sarocesch.sarosmoneymod.world.inventory.ATMGUIMenu;
import io.netty.buffer.Unpooled;
import net.minecraft.core.BlockPos;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.MenuProvider;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.state.BlockState;
import net.neoforged.neoforge.event.entity.player.PlayerInteractEvent;
import net.neoforged.bus.api.EventPriority;
import net.neoforged.bus.api.SubscribeEvent;
import net.neoforged.fml.common.Mod;

@Mod.EventBusSubscriber(modid = "saros__money_mod")
public class ATMInteractionListener {

    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public static void onRightClickBlock(PlayerInteractEvent.RightClickBlock event) {
        Player player = event.getEntity();
        Level level = event.getLevel();
        BlockPos pos = event.getPos();
        BlockState state = level.getBlockState(pos);

        // Check if the clicked block is one of our ATM blocks
        if (state.getBlock() instanceof ATMBlock ||
                state.getBlock() instanceof ATM2Block ||
                state.getBlock() instanceof ATM3Block) {

            // Cancel the vanilla interaction
            event.setCancellationResult(InteractionResult.SUCCESS);
            event.setCanceled(true);

            // Only open the GUI on the server side
            if (!level.isClientSide() && player instanceof ServerPlayer serverPlayer) {
                serverPlayer.openMenu(new MenuProvider() {
                    @Override
                    public Component getDisplayName() {
                        return Component.literal("ATM");
                    }

                    @Override
                    public AbstractContainerMenu createMenu(int id, Inventory inventory, Player player) {
                        // Schreibe BlockPosition und Hand in den Puffer
                        FriendlyByteBuf packetBuffer = new FriendlyByteBuf(Unpooled.buffer());
                        packetBuffer.writeBlockPos(pos);
                        return new ATMGUIMenu(id, inventory, packetBuffer);
                    }
                }, buf -> {
                    // Schreibe zusätzliche Daten für das Menü
                    buf.writeBlockPos(pos);
                });
            }
        }
    }
}