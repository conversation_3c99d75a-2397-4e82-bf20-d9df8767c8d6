package de.sarocesch.sarosmoneymod.listener;

import de.sarocesch.sarosmoneymod.Config;
import de.sarocesch.sarosmoneymod.data.BalanceData;
import de.sarocesch.sarosmoneymod.data.BalanceManager;
import de.sarocesch.sarosmoneymod.data.BalanceMigrator;
import net.neoforged.fml.event.lifecycle.FMLCommonSetupEvent;
import net.neoforged.fml.event.lifecycle.FMLClientSetupEvent;
import net.neoforged.fml.common.Mod;
import net.neoforged.fml.javafmlmod.FMLJavaModLoadingContext;
import net.neoforged.bus.api.SubscribeEvent;
import net.neoforged.neoforge.event.server.ServerAboutToStartEvent;
import net.neoforged.neoforge.event.entity.player.PlayerEvent;
import net.neoforged.api.distmarker.OnlyIn;
import net.neoforged.api.distmarker.Dist;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;

@Mod.EventBusSubscriber(bus = Mod.EventBusSubscriber.Bus.MOD)
public class BalanceJoin {

    public BalanceJoin() {
    }

    @SubscribeEvent
    public static void init(FMLCommonSetupEvent event) {
        // Erzeugen der Instanz, falls benötigt
        new BalanceJoin();
    }

    @Mod.EventBusSubscriber(bus = Mod.EventBusSubscriber.Bus.GAME)
    private static class GameEvents {
        @SubscribeEvent
        public static void serverLoad(ServerAboutToStartEvent event) {
            MinecraftServer server = event.getServer();
            // Migration alter Daten (falls vorhanden)
            BalanceMigrator.migrateOldData(server);
        }

        @SubscribeEvent
        public static void onPlayerJoin(PlayerEvent.PlayerLoggedIn event) {
            if (event.getEntity() instanceof ServerPlayer) {
                ServerPlayer player = (ServerPlayer) event.getEntity();
                MinecraftServer server = player.getServer();
                if (server == null) return;

                // Initialisiere den Spieler nur, wenn noch kein Eintrag existiert.
                if (!BalanceData.get(server).containsBalance(player.getUUID().toString())) {
                    BalanceManager.setBalance(player.getUUID().toString(), Config.START_MONEY);
                }
            }
        }
    }

    @Mod.EventBusSubscriber(value = Dist.CLIENT, bus = Mod.EventBusSubscriber.Bus.MOD)
    private static class ClientEvents {
        @SubscribeEvent
        public static void clientLoad(FMLClientSetupEvent event) {
            // Client-spezifische Initialisierungen, falls erforderlich
        }
    }
}