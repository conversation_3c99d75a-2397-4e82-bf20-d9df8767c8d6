package de.sarocesch.sarosmoneymod.listener;

import de.sarocesch.sarosmoneymod.Config;
import de.sarocesch.sarosmoneymod.data.BalanceManager;
import net.minecraft.server.level.ServerPlayer;
import net.neoforged.neoforge.event.tick.ServerTickEvent;
import net.neoforged.bus.api.SubscribeEvent;
import net.neoforged.fml.common.EventBusSubscriber;
import net.minecraft.server.MinecraftServer;
import net.neoforged.neoforge.common.NeoForge;
import net.minecraft.network.chat.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Mod.EventBusSubscriber(bus = Mod.EventBusSubscriber.Bus.MOD)
public class Paycheck {
    private static long lastPaycheckTime = 0;
    private static final long PAYCHECK_INTERVAL = TimeUnit.MINUTES.toMillis(30);

    @SubscribeEvent
    public static void init(TickEvent.ServerTickEvent event) {
        // Registriere den Tick-Handler beim Game-Bus
        NeoForge.EVENT_BUS.register(new PaycheckHandler());
    }

    public static class PaycheckHandler {
        @SubscribeEvent
        public void onServerTick(TickEvent.ServerTickEvent event) {
            if (event.phase != TickEvent.Phase.END) return;

            MinecraftServer server = event.getServer();
            long currentTime = System.currentTimeMillis();

            if (Config.PAYCHECK_ENABLED && (currentTime - lastPaycheckTime >= PAYCHECK_INTERVAL)) {
                lastPaycheckTime = currentTime;
                payPlayers(server);
            }
        }

        private void payPlayers(MinecraftServer server) {
            List<ServerPlayer> players = server.getPlayerList().getPlayers();
            for (ServerPlayer player : players) {
                String playerUUID = player.getUUID().toString();
                int paycheckAmount = Config.PAYCHECK_AMOUNT;

                BalanceManager.addMoneyToPlayer(server, playerUUID, paycheckAmount);

                player.sendSystemMessage(Component.translatable("message.sarosmoneymod.paycheck", paycheckAmount));
            }
        }
    }
}