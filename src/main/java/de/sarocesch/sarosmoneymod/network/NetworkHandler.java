package de.sarocesch.sarosmoneymod.network;

import de.sarocesch.sarosmoneymod.SarosMoneyModMod;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.neoforged.neoforge.network.PacketDistributor;
import net.neoforged.neoforge.network.event.RegisterPayloadHandlerEvent;
import net.neoforged.neoforge.network.registration.IPayloadRegistrar;

public class NetworkHandler {
    // NeoForge Netzwerkkanal-Konfiguration
    public static void register(final RegisterPayloadHandlerEvent event) {
        final IPayloadRegistrar registrar = event.registrar(SarosMoneyModMod.MODID)
                .versioned("1.0.0") // Protokollversion
                .optional();

        // Registriere Nachricht (Client -> Server)
        registrar.play(
                ATMGUIButtonMessage.ID,
                ATMGUIButtonMessage::new,
                handler -> handler
                        .server(ATMGUIButtonMessage::handle)
                        .encoder(ATMGUIButtonMessage::write)
                        .decoder(ATMGUIButtonMessage::new)
        );
    }

    public static void sendToServer(ATMGUIButtonMessage message) {
        PacketDistributor.SERVER.noArg().send(message);
    }
}