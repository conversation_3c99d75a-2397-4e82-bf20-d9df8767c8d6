package de.sarocesch.sarosmoneymod.procedures;

import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.core.BlockPos;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.block.Block;
import de.sarocesch.sarosmoneymod.init.SarosMoneyModModBlocks;
import net.minecraft.world.item.Item;

public class ATM2BlockAddedProcedure {
    public static void execute(LevelAccessor world, double x, double y, double z) {
        // Konvertierung mit BlockPos.containing für präzise Positionierung
        BlockPos lowerBlockPos = BlockPos.containing(x, y, z);
        BlockPos upperBlockPos = BlockPos.containing(x, y + 1, z);

        BlockState lowerBlockState = world.getBlockState(lowerBlockPos);

        if (world.getBlockState(upperBlockPos).is(Blocks.AIR)) {
            BlockState newBlockState = SarosMoneyModModBlocks.ATM_2.get().defaultBlockState();

            // Rotationseigenschaft übertragen
            if (lowerBlockState.hasProperty(BlockStateProperties.HORIZONTAL_FACING)) {
                newBlockState = newBlockState.setValue(
                        BlockStateProperties.HORIZONTAL_FACING,
                        lowerBlockState.getValue(BlockStateProperties.HORIZONTAL_FACING)
                );
            }

            world.setBlock(upperBlockPos, newBlockState, Block.UPDATE_ALL);
        } else {
            // Block droppen mit verbessertem ItemStack-Handling
            if (!world.isClientSide()) {
                Block.popResource(
                        (net.minecraft.world.level.Level) world,
                        lowerBlockPos,
                        new ItemStack(SarosMoneyModModBlocks.ATM_2.get())
                );
            }
        }

        // Block sicher entfernen
        world.removeBlock(lowerBlockPos, false);
    }
}